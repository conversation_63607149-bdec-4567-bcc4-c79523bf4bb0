-s dir
--name sing-box
--category net
--license GPL-3.0-or-later
--description "The universal proxy platform."
--url "https://sing-box.sagernet.org/"
--maintainer "ne<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"
--no-deb-generate-changes

--config-files /etc/config/sing-box
--config-files /etc/sing-box/config.json

--depends ca-bundle
--depends kmod-inet-diag
--depends kmod-tun
--depends firewall4

--before-remove release/config/openwrt.prerm

release/config/config.json=/etc/sing-box/config.json

release/config/openwrt.conf=/etc/config/sing-box
release/config/openwrt.init=/etc/init.d/sing-box
release/config/openwrt.keep=/lib/upgrade/keep.d/sing-box

release/completions/sing-box.bash=/usr/share/bash-completion/completions/sing-box.bash
release/completions/sing-box.fish=/usr/share/fish/vendor_completions.d/sing-box.fish
release/completions/sing-box.zsh=/usr/share/zsh/site-functions/_sing-box

LICENSE=/usr/share/licenses/sing-box/LICENSE
