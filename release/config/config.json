{"log": {"level": "info"}, "dns": {"servers": [{"address": "tls://*******"}]}, "inbounds": [{"type": "shadowsocks", "listen": "::", "listen_port": 8080, "network": "tcp", "method": "2022-blake3-aes-128-gcm", "password": "Gn1JUS14bLUHgv1cWDDp4A==", "multiplex": {"enabled": true, "padding": true}}], "outbounds": [{"type": "direct"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"port": 53, "outbound": "dns-out"}]}}