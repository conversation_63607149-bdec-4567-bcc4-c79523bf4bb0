# DNS性能测试工具

这是一个用于测试不同场景下DNS解析效率的Go程序，专门用于测试sing-box在不同模式下的DNS性能。

## 功能特性

- 支持多轮测试并计算平均值
- 测试18个主流网站（9个中国大陆 + 9个国际网站）
- 详细的性能统计和分析
- 支持自定义DNS服务器和超时时间

## 测试场景

### 场景1: sing-box + hijack模式
- 通过sing-box的DNS劫持功能进行解析
- 需要手动配置sing-box为hijack模式

### 场景2: sing-box + detour模式  
- 使用sing-box内置的DNS设置进行解析
- 需要手动配置sing-box为detour模式

## 测试网站列表

### 中国大陆网站
- baidu.com
- qq.com
- taobao.com
- weibo.com
- zhihu.com
- bilibili.com
- jd.com
- sina.com.cn
- 163.com

### 国际网站
- google.com
- youtube.com
- facebook.com
- twitter.com
- instagram.com
- github.com
- stackoverflow.com
- reddit.com
- wikipedia.org

## 使用方法

### 编译程序
```bash
cd dns-performance-test
go mod tidy
go build -o dns-test
```

### 运行测试
```bash
# 基本测试（默认3轮）
./dns-test

# 指定测试轮数
./dns-test -r 5

# 指定DNS服务器
./dns-test -d *******:53

# 指定超时时间
./dns-test -t 10

# 组合参数
./dns-test -r 5 -d *******:53 -t 3
```

### 参数说明
- `-r, --rounds`: 测试轮数，默认3轮
- `-d, --dns`: DNS服务器地址，默认*******:53
- `-t, --timeout`: DNS查询超时时间（秒），默认5秒

## 输出说明

程序会输出以下信息：
1. 每轮测试的详细结果
2. 每个域名的解析时间或错误信息
3. 每轮的统计信息（成功率、平均延迟、最快/最慢查询）
4. 多轮测试的平均结果
5. 中国大陆网站和国际网站的分类统计

## 测试流程

1. 配置sing-box为hijack模式
2. 运行测试程序记录结果
3. 配置sing-box为detour模式
4. 再次运行测试程序记录结果
5. 对比两种模式的性能差异

## 注意事项

- 场景切换需要手动修改sing-box配置
- 建议在相同网络环境下进行对比测试
- 多轮测试可以减少网络波动的影响
- 测试结果会受到网络状况、DNS服务器负载等因素影响
