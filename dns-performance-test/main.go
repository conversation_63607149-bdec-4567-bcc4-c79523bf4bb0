package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"sort"
	"time"

	"github.com/spf13/cobra"
)

// 测试网站列表
var (
	// 中国大陆网站
	chineseWebsites = []string{
		"baidu.com",
		"qq.com", 
		"taobao.com",
		"weibo.com",
		"zhihu.com",
		"bilibili.com",
		"jd.com",
		"sina.com.cn",
		"163.com",
	}
	
	// 国际网站
	internationalWebsites = []string{
		"google.com",
		"youtube.com",
		"facebook.com",
		"twitter.com",
		"instagram.com",
		"github.com",
		"stackoverflow.com",
		"reddit.com",
		"wikipedia.org",
	}
)

type DNSResult struct {
	Domain   string
	Duration time.Duration
	Success  bool
	Error    string
}

type TestResults struct {
	ChineseResults       []DNSResult
	InternationalResults []DNSResult
	TotalTests          int
	SuccessfulTests     int
	AverageLatency      time.Duration
}

func main() {
	var rootCmd = &cobra.Command{
		Use:   "dns-performance-test",
		Short: "DNS解析性能测试工具",
		Long:  "测试不同场景下DNS解析的效率，支持singbox+hijack模式和singbox+detour模式",
	}

	var rounds int
	var timeout int

	rootCmd.Flags().IntVarP(&rounds, "rounds", "r", 3, "测试轮数（取平均值）")
	rootCmd.Flags().IntVarP(&timeout, "timeout", "t", 5, "DNS查询超时时间（秒）")

	rootCmd.Run = func(cmd *cobra.Command, args []string) {
		runDNSTest(rounds, time.Duration(timeout)*time.Second)
	}

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}

func runDNSTest(rounds int, timeout time.Duration) {
	fmt.Printf("=== DNS性能测试 ===\n")
	fmt.Printf("测试轮数: %d\n", rounds)
	fmt.Printf("使用系统DNS设置（通过代理软件）\n")
	fmt.Printf("超时时间: %v\n", timeout)
	fmt.Printf("测试时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	allResults := make([]TestResults, rounds)

	for round := 1; round <= rounds; round++ {
		fmt.Printf("--- 第 %d 轮测试 ---\n", round)
		results := performSingleRound(timeout)
		allResults[round-1] = results
		
		printRoundResults(results, round)
		
		if round < rounds {
			fmt.Printf("等待下一轮测试...\n\n")
			time.Sleep(2 * time.Second)
		}
	}

	printAverageResults(allResults)
}

func performSingleRound(timeout time.Duration) TestResults {
	results := TestResults{}
	
	// 测试中国大陆网站
	fmt.Printf("测试中国大陆网站:\n")
	for _, domain := range chineseWebsites {
		result := queryDNS(domain, timeout)
		results.ChineseResults = append(results.ChineseResults, result)
		fmt.Printf("  %s: ", domain)
		if result.Success {
			fmt.Printf("✓ %v\n", result.Duration)
		} else {
			fmt.Printf("✗ %s\n", result.Error)
		}
	}

	fmt.Printf("\n测试国际网站:\n")
	// 测试国际网站
	for _, domain := range internationalWebsites {
		result := queryDNS(domain, timeout)
		results.InternationalResults = append(results.InternationalResults, result)
		fmt.Printf("  %s: ", domain)
		if result.Success {
			fmt.Printf("✓ %v\n", result.Duration)
		} else {
			fmt.Printf("✗ %s\n", result.Error)
		}
	}

	// 计算统计信息
	calculateStats(&results)
	
	return results
}

func queryDNS(domain string, timeout time.Duration) DNSResult {
	result := DNSResult{Domain: domain}

	start := time.Now()

	// 使用系统默认DNS解析（会通过代理软件的设置）
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 执行DNS查询 - 使用系统默认解析器，会受到代理软件DNS设置影响
	_, err := net.DefaultResolver.LookupIPAddr(ctx, domain)

	result.Duration = time.Since(start)

	if err != nil {
		result.Success = false
		result.Error = err.Error()
	} else {
		result.Success = true
	}

	return result
}

func calculateStats(results *TestResults) {
	var totalDuration time.Duration
	var successCount int
	
	allResults := append(results.ChineseResults, results.InternationalResults...)
	results.TotalTests = len(allResults)
	
	for _, result := range allResults {
		if result.Success {
			totalDuration += result.Duration
			successCount++
		}
	}
	
	results.SuccessfulTests = successCount
	if successCount > 0 {
		results.AverageLatency = totalDuration / time.Duration(successCount)
	}
}

func printRoundResults(results TestResults, round int) {
	fmt.Printf("\n第 %d 轮测试结果:\n", round)
	fmt.Printf("总测试数: %d\n", results.TotalTests)
	fmt.Printf("成功数: %d\n", results.SuccessfulTests)
	fmt.Printf("成功率: %.2f%%\n", float64(results.SuccessfulTests)/float64(results.TotalTests)*100)
	fmt.Printf("平均延迟: %v\n", results.AverageLatency)
	
	// 显示最快和最慢的查询
	var successfulResults []DNSResult
	for _, result := range append(results.ChineseResults, results.InternationalResults...) {
		if result.Success {
			successfulResults = append(successfulResults, result)
		}
	}
	
	if len(successfulResults) > 0 {
		sort.Slice(successfulResults, func(i, j int) bool {
			return successfulResults[i].Duration < successfulResults[j].Duration
		})
		
		fmt.Printf("最快查询: %s (%v)\n", successfulResults[0].Domain, successfulResults[0].Duration)
		fmt.Printf("最慢查询: %s (%v)\n", successfulResults[len(successfulResults)-1].Domain, successfulResults[len(successfulResults)-1].Duration)
	}
	
	fmt.Printf("\n")
}

func printAverageResults(allResults []TestResults) {
	fmt.Printf("=== 多轮测试平均结果 ===\n")
	
	var totalSuccessful int
	var totalTests int
	var totalLatency time.Duration
	var successfulRounds int
	
	for _, result := range allResults {
		totalTests += result.TotalTests
		totalSuccessful += result.SuccessfulTests
		if result.SuccessfulTests > 0 {
			totalLatency += result.AverageLatency
			successfulRounds++
		}
	}
	
	fmt.Printf("总轮数: %d\n", len(allResults))
	fmt.Printf("总测试数: %d\n", totalTests)
	fmt.Printf("总成功数: %d\n", totalSuccessful)
	fmt.Printf("平均成功率: %.2f%%\n", float64(totalSuccessful)/float64(totalTests)*100)
	
	if successfulRounds > 0 {
		avgLatency := totalLatency / time.Duration(successfulRounds)
		fmt.Printf("平均延迟: %v\n", avgLatency)
	}
	
	// 分别计算中国大陆和国际网站的平均延迟
	printDomainCategoryStats("中国大陆网站", allResults, true)
	printDomainCategoryStats("国际网站", allResults, false)
}

func printDomainCategoryStats(category string, allResults []TestResults, isChinese bool) {
	var totalDuration time.Duration
	var successCount int
	
	for _, roundResult := range allResults {
		var results []DNSResult
		if isChinese {
			results = roundResult.ChineseResults
		} else {
			results = roundResult.InternationalResults
		}
		
		for _, result := range results {
			if result.Success {
				totalDuration += result.Duration
				successCount++
			}
		}
	}
	
	if successCount > 0 {
		avgLatency := totalDuration / time.Duration(successCount)
		fmt.Printf("%s平均延迟: %v (成功查询数: %d)\n", category, avgLatency, successCount)
	}
}
