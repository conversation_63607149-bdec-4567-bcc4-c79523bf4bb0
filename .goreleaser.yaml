version: 2
project_name: sing-box
builds:
  - &template
    id: main
    main: ./cmd/sing-box
    flags:
      - -v
      - -trimpath
    ldflags:
      - -X github.com/sagernet/sing-box/constant.Version={{ .Version }}
      - -s
      - -buildid=
    tags:
      - with_gvisor
      - with_quic
      - with_dhcp
      - with_wireguard
      - with_utls
      - with_acme
      - with_clash_api
      - with_tailscale
    env:
      - CGO_ENABLED=0
      - GOTOOLCHAIN=local
    targets:
      - linux_386
      - linux_amd64_v1
      - linux_arm64
      - linux_arm_6
      - linux_arm_7
      - linux_s390x
      - linux_riscv64
      - linux_mips64le
      - windows_amd64_v1
      - windows_386
      - windows_arm64
      - darwin_amd64_v1
      - darwin_arm64
    mod_timestamp: '{{ .CommitTimestamp }}'
  - id: legacy
    <<: *template
    tags:
      - with_gvisor
      - with_quic
      - with_dhcp
      - with_wireguard
      - with_utls
      - with_acme
      - with_clash_api
      - with_tailscale
    env:
      - CGO_ENABLED=0
      - GOROOT={{ .Env.GOPATH }}/go_legacy
    tool: "{{ .Env.GOPATH }}/go_legacy/bin/go"
    targets:
      - windows_amd64_v1
      - windows_386
  - id: android
    <<: *template
    env:
      - CGO_ENABLED=1
      - GOTOOLCHAIN=local
    overrides:
      - goos: android
        goarch: arm
        goarm: 7
        env:
          - CC=armv7a-linux-androideabi21-clang
          - CXX=armv7a-linux-androideabi21-clang++
      - goos: android
        goarch: arm64
        env:
          - CC=aarch64-linux-android21-clang
          - CXX=aarch64-linux-android21-clang++
      - goos: android
        goarch: 386
        env:
          - CC=i686-linux-android21-clang
          - CXX=i686-linux-android21-clang++
      - goos: android
        goarch: amd64
        goamd64: v1
        env:
          - CC=x86_64-linux-android21-clang
          - CXX=x86_64-linux-android21-clang++
    targets:
      - android_arm_7
      - android_arm64
      - android_386
      - android_amd64
archives:
  - &template
    id: archive
    builds:
      - main
      - android
    formats:
      - tar.gz
    format_overrides:
      - goos: windows
        formats:
          - zip
    wrap_in_directory: true
    files:
      - LICENSE
    name_template: '{{ .ProjectName }}-{{ .Version }}-{{ .Os }}-{{ .Arch }}{{ with .Arm }}v{{ . }}{{ end }}{{ if and .Mips (not (eq .Mips "hardfloat")) }}_{{ .Mips }}{{ end }}{{ if not (eq .Amd64 "v1") }}{{ .Amd64 }}{{ end }}'
  - id: archive-legacy
    <<: *template
    builds:
      - legacy
    name_template: '{{ .ProjectName }}-{{ .Version }}-{{ .Os }}-{{ .Arch }}-legacy'
nfpms:
  - id: package
    package_name: sing-box
    file_name_template: '{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ with .Arm }}v{{ . }}{{ end }}{{ if and .Mips (not (eq .Mips "hardfloat")) }}_{{ .Mips }}{{ end }}{{ if not (eq .Amd64 "v1") }}{{ .Amd64 }}{{ end }}'
    builds:
      - main
    homepage: https://sing-box.sagernet.org/
    maintainer: nekohasekai <<EMAIL>>
    description: The universal proxy platform.
    license: GPLv3 or later
    formats:
      - deb
      - rpm
      - archlinux
#      - apk
#      - ipk
    priority: extra
    contents:
      - src: release/config/config.json
        dst: /etc/sing-box/config.json
        type: "config|noreplace"

      - src: release/config/sing-box.service
        dst: /usr/lib/systemd/system/sing-box.service
      - src: release/config/sing-box@.service
        dst: /usr/lib/systemd/system/sing-box@.service
      - src: release/config/sing-box.sysusers
        dst: /usr/lib/sysusers.d/sing-box.conf
      - src: release/config/sing-box.rules
        dst: /usr/share/polkit-1/rules.d/sing-box.rules
      - src: release/config/sing-box-split-dns.xml
        dst: /usr/share/dbus-1/system.d/sing-box-split-dns.conf

      - src: release/completions/sing-box.bash
        dst: /usr/share/bash-completion/completions/sing-box.bash
      - src: release/completions/sing-box.fish
        dst: /usr/share/fish/vendor_completions.d/sing-box.fish
      - src: release/completions/sing-box.zsh
        dst: /usr/share/zsh/site-functions/_sing-box

      - src: LICENSE
        dst: /usr/share/licenses/sing-box/LICENSE
    deb:
      signature:
        key_file: "{{ .Env.NFPM_KEY_PATH }}"
      fields:
        Bugs: https://github.com/SagerNet/sing-box/issues
    rpm:
      signature:
        key_file: "{{ .Env.NFPM_KEY_PATH }}"
    overrides:
      apk:
        contents:
          - src: release/config/config.json
            dst: /etc/sing-box/config.json
            type: config

          - src: release/config/sing-box.initd
            dst: /etc/init.d/sing-box

          - src: release/completions/sing-box.bash
            dst: /usr/share/bash-completion/completions/sing-box.bash
          - src: release/completions/sing-box.fish
            dst: /usr/share/fish/vendor_completions.d/sing-box.fish
          - src: release/completions/sing-box.zsh
            dst: /usr/share/zsh/site-functions/_sing-box

          - src: LICENSE
            dst: /usr/share/licenses/sing-box/LICENSE
      ipk:
        contents:
          - src: release/config/config.json
            dst: /etc/sing-box/config.json
            type: config

          - src: release/config/openwrt.init
            dst: /etc/init.d/sing-box
          - src: release/config/openwrt.conf
            dst: /etc/config/sing-box
source:
  enabled: false
  name_template: '{{ .ProjectName }}-{{ .Version }}.source'
  prefix_template: '{{ .ProjectName }}-{{ .Version }}/'
checksum:
  disable: true
  name_template: '{{ .ProjectName }}-{{ .Version }}.checksum'
signs:
  - artifacts: checksum
release:
  github:
    owner: SagerNet
    name: sing-box
  draft: true
  prerelease: auto
  mode: replace
  ids:
    - archive
    - package
  skip_upload: true
partial:
  by: target