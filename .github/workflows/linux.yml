name: Build Linux Packages

on:
  workflow_dispatch:
    inputs:
      version:
        description: "Version name"
        required: true
        type: string
      forceBeta:
        description: "Force beta"
        required: false
        type: boolean
        default: false
  release:
    types:
      - published

jobs:
  calculate_version:
    name: Calculate version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.outputs.outputs.version }}
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ^1.25.0
      - name: Check input version
        if: github.event_name == 'workflow_dispatch'
        run: |-
          echo "version=${{ inputs.version }}"
          echo "version=${{ inputs.version }}" >> "$GITHUB_ENV"
      - name: Calculate version
        if: github.event_name != 'workflow_dispatch'
        run: |-
          go run -v ./cmd/internal/read_tag --ci --nightly
      - name: Set outputs
        id: outputs
        run: |-
          echo "version=$version" >> "$GITHUB_OUTPUT"
  build:
    name: Build binary
    runs-on: ubuntu-latest
    needs:
      - calculate_version
    strategy:
      matrix:
        include:
          - { os: linux, arch: amd64, debian: amd64, rpm: x86_64, pacman: x86_64 }
          - { os: linux, arch: "386", debian: i386, rpm: i386 }
          - { os: linux, arch: arm, goarm: "6", debian: armel, rpm: armv6hl }
          - { os: linux, arch: arm, goarm: "7", debian: armhf, rpm: armv7hl, pacman: armv7hl }
          - { os: linux, arch: arm64, debian: arm64, rpm: aarch64, pacman: aarch64 }
          - { os: linux, arch: mips64le, debian: mips64el, rpm: mips64el }
          - { os: linux, arch: mipsle, debian: mipsel, rpm: mipsel }
          - { os: linux, arch: s390x, debian: s390x, rpm: s390x }
          - { os: linux, arch: ppc64le, debian: ppc64el, rpm: ppc64le }
          - { os: linux, arch: riscv64, debian: riscv64, rpm: riscv64 }
          - { os: linux, arch: loong64, debian: loongarch64, rpm: loongarch64 }
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ^1.25.0
      - name: Setup Android NDK
        if: matrix.os == 'android'
        uses: nttld/setup-ndk@v1
        with:
          ndk-version: r28
          local-cache: true
      - name: Set tag
        run: |-
          git ls-remote --exit-code --tags origin v${{ needs.calculate_version.outputs.version }} || echo "PUBLISHED=false" >> "$GITHUB_ENV"
          git tag v${{ needs.calculate_version.outputs.version }} -f
      - name: Set build tags
        run: |
          set -xeuo pipefail
          TAGS='with_gvisor,with_quic,with_dhcp,with_wireguard,with_utls,with_acme,with_clash_api,with_tailscale'
          echo "BUILD_TAGS=${TAGS}" >> "${GITHUB_ENV}"
      - name: Build
        run: |
          set -xeuo pipefail
          mkdir -p dist
          go build -v -trimpath -o dist/sing-box -tags "${BUILD_TAGS}" \
          -ldflags '-s -buildid= -X github.com/sagernet/sing-box/constant.Version=${{ needs.calculate_version.outputs.version }}' \
          ./cmd/sing-box
        env:
          CGO_ENABLED: "0"
          GOOS: ${{ matrix.os }}
          GOARCH: ${{ matrix.arch }}
          GOARM: ${{ matrix.goarm }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Set mtime
        run: |-
          TZ=UTC touch -t '197001010000' dist/sing-box
      - name: Set name
        if: (! contains(needs.calculate_version.outputs.version, '-')) && !inputs.forceBeta
        run: |-
          echo "NAME=sing-box" >> "$GITHUB_ENV"
      - name: Set beta name
        if: contains(needs.calculate_version.outputs.version, '-') || inputs.forceBeta
        run: |-
          echo "NAME=sing-box-beta" >> "$GITHUB_ENV"
      - name: Set version
        run: |-
          PKG_VERSION="${{ needs.calculate_version.outputs.version }}"
          PKG_VERSION="${PKG_VERSION//-/\~}"
          echo "PKG_VERSION=${PKG_VERSION}" >> "${GITHUB_ENV}"
      - name: Package DEB
        if: matrix.debian != ''
        run: |
          set -xeuo pipefail
          sudo gem install fpm
          sudo apt-get install -y debsigs
          cp .fpm_systemd .fpm
          fpm -t deb \
            --name "${NAME}" \
            -v "$PKG_VERSION" \
            -p "dist/${NAME}_${{ needs.calculate_version.outputs.version }}_linux_${{ matrix.debian }}.deb" \
            --architecture ${{ matrix.debian }} \
            dist/sing-box=/usr/bin/sing-box
          curl -Lo '/tmp/debsigs.diff' 'https://gitlab.com/debsigs/debsigs/-/commit/160138f5de1ec110376d3c807b60a37388bc7c90.diff'
          sudo patch /usr/bin/debsigs < '/tmp/debsigs.diff'
          rm -rf $HOME/.gnupg
          gpg --pinentry-mode loopback --passphrase "${{ secrets.GPG_PASSPHRASE }}" --import <<EOF
          ${{ secrets.GPG_KEY }}
          EOF
          debsigs --sign=origin -k ${{ secrets.GPG_KEY_ID }} --gpgopts '--pinentry-mode loopback --passphrase "${{ secrets.GPG_PASSPHRASE }}"' dist/*.deb
      - name: Package RPM
        if: matrix.rpm != ''
        run: |-
          set -xeuo pipefail
          sudo gem install fpm
          cp .fpm_systemd .fpm
          fpm -t rpm \
            --name "${NAME}" \
            -v "$PKG_VERSION" \
            -p "dist/${NAME}_${{ needs.calculate_version.outputs.version }}_linux_${{ matrix.rpm }}.rpm" \
            --architecture ${{ matrix.rpm }} \
            dist/sing-box=/usr/bin/sing-box
          cat > $HOME/.rpmmacros <<EOF
          %_gpg_name ${{ secrets.GPG_KEY_ID }}
          %_gpg_sign_cmd_extra_args --pinentry-mode loopback --passphrase ${{ secrets.GPG_PASSPHRASE }}
          EOF
          gpg --pinentry-mode loopback --passphrase "${{ secrets.GPG_PASSPHRASE }}" --import <<EOF
          ${{ secrets.GPG_KEY }}
          EOF
          rpmsign --addsign dist/*.rpm
      - name: Cleanup
        run: rm dist/sing-box
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binary-${{ matrix.os }}_${{ matrix.arch }}${{ matrix.goarm && format('v{0}', matrix.goarm) }}${{ matrix.legacy_go && '-legacy' || '' }}
          path: "dist"
  upload:
    name: Upload builds
    runs-on: ubuntu-latest
    needs:
      - calculate_version
      - build
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 0
      - name: Set tag
        run: |-
          git ls-remote --exit-code --tags origin v${{ needs.calculate_version.outputs.version }} || echo "PUBLISHED=false" >> "$GITHUB_ENV"
          git tag v${{ needs.calculate_version.outputs.version }} -f
          echo "VERSION=${{ needs.calculate_version.outputs.version }}" >> "$GITHUB_ENV"
      - name: Download builds
        uses: actions/download-artifact@v5
        with:
          path: dist
          merge-multiple: true
      - name: Publish packages
        run: |-
          ls dist | xargs -I {} curl -F "package=@dist/{}" https://${{ secrets.FURY_TOKEN }}@push.fury.io/sagernet/
