name: Lint

on:
  push:
    branches:
      - stable-next
      - main-next
      - dev-next
    paths-ignore:
      - '**.md'
      - '.github/**'
      - '!.github/workflows/lint.yml'
  pull_request:
    branches:
      - stable-next
      - main-next
      - dev-next

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ~1.24.6
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: latest
          args: --timeout=30m
          install-mode: binary
          verify: false
