name: 错误反馈
description: "提交 sing-box 漏洞"
body:
  - type: dropdown
    attributes:
      label: 操作系统
      description: 请提供操作系统类型
      options:
        - iOS
        - macOS
        - Apple tvOS
        - Android
        - Windows
        - Linux
        - 其他
    validations:
      required: true
  - type: input
    attributes:
      label: 系统版本
      description: 请提供操作系统版本
    validations:
      required: true
  - type: dropdown
    attributes:
      label: 安装类型
      description: 请提供该 sing-box 安装类型
      options:
        - sing-box 原始命令行程序
        - sing-box for iOS 图形客户端程序
        - sing-box for macOS 图形客户端程序
        - sing-box for Apple tvOS 图形客户端程序
        - sing-box for Android 图形客户端程序
        - 宣传使用 sing-box 的第三方图形客户端程序 (Windows)
        - 宣传使用 sing-box 的第三方图形客户端程序 (Android)
        - 其他
    validations:
      required: true
  - type: input
    attributes:
      description: 图形客户端版本
      label: 如果您使用图形客户端程序，请提供该程序版本。
  - type: textarea
    attributes:
      label: 版本
      description: 如果您使用原始命令行程序，请提供 `sing-box version` 命令的输出。
      render: shell
  - type: textarea
    attributes:
      label: 描述
      description: 请提供错误的详细描述。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 重现方式
      description: 请提供重现错误的步骤，必须包括可以在本地（不依赖与远程服务器）使用 sing-box 原始命令行程序重现错误的配置文件与流程。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 日志
      description: |-
        此外，如果您遭遇图形界面应用程序崩溃，请附加提供崩溃日志。
        对于 Apple 平台图形客户端程序，请检查 `Settings - View Service Log` 以导出崩溃日志。
        对于 Android 图形客户端程序，请检查 `/sdcard/Android/data/io.nekohasekai.sfa/files/stderr.log` 文件以导出崩溃日志。
      render: shell
  - type: checkboxes
    id: supporter
    attributes:
      label: 支持我们
      options:
        - label: 我已经 [赞助](https://github.com/sponsors/nekohasekai/)
  - type: checkboxes
    attributes:
      label: 完整性要求
      description: |-
        请勾选以下所有选项以证明您已经阅读并理解了以下要求，否则该 issue 将被关闭。
        sing-box 不是讨好无法作出任何意义上的贡献的最终用户并获取非道德影响力的项目，如果您在此处欺骗以故意浪费开发者的时间，您将被永久封锁。
      options:
        - label: 我保证阅读了文档，了解所有我编写的配置文件项的含义，而不是大量堆砌看似有用的选项或默认值。
          required: true
        - label: 我保证提供了可以在本地重现该问题的服务器、客户端配置文件与流程，而不是一个脱敏的复杂客户端配置文件。
          required: true
        - label: 我保证提供了可用于重现我报告的错误的最简配置，而不是依赖远程服务器、TUN、图形界面客户端或者其他闭源软件。
          required: true
        - label: 我保证提供了完整的配置文件与日志，而不是出于对自身智力的自信而仅提供了部分认为有用的部分。
          required: true
