name: Bug report
description: "Report sing-box bug"
body:
  - type: dropdown
    attributes:
      label: Operating system
      description: Operating system type
      options:
        - iOS
        - macOS
        - Apple tvOS
        - Android
        - Windows
        - Linux
        - Others
    validations:
      required: true
  - type: input
    attributes:
      label: System version
      description: Please provide the operating system version
    validations:
      required: true
  - type: dropdown
    attributes:
      label: Installation type
      description: Please provide the sing-box installation type
      options:
        - Original sing-box Command Line
        - sing-box for iOS Graphical Client
        - sing-box for macOS Graphical Client
        - sing-box for Apple tvOS Graphical Client
        - sing-box for Android Graphical Client
        - Third-party graphical clients that advertise themselves as using sing-box (Windows)
        - Third-party graphical clients that advertise themselves as using sing-box (Android)
        - Others
    validations:
      required: true
  - type: input
    attributes:
      description: Graphical client version
      label: If you are using a graphical client, please provide the version of the client.
  - type: textarea
    attributes:
      label: Version
      description: If you are using the original command line program, please provide the output of the `sing-box version` command.
      render: shell
  - type: textarea
    attributes:
      label: Description
      description: Please provide a detailed description of the error.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction
      description: Please provide the steps to reproduce the error, including the configuration files and procedures that can locally (not dependent on the remote server) reproduce the error using the original command line program of sing-box.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Logs
      description: |-
        In addition, if you encounter a crash with the graphical client, please also provide crash logs.
        For Apple platform clients, please check `Settings - View Service Log` for crash logs.
        For the Android client, please check the `/sdcard/Android/data/io.nekohasekai.sfa/files/stderr.log` file for crash logs.
      render: shell
  - type: checkboxes
    id: supporter
    attributes:
      label: Supporter
      options:
        - label: I am a [sponsor](https://github.com/sponsors/nekohasekai/)
  - type: checkboxes
    attributes:
      label: Integrity requirements
      description: |-
        Please check all of the following options to prove that you have read and understood the requirements, otherwise this issue will be closed.
        Sing-box is not a project aimed to please users who can't make any meaningful contributions and gain unethical influence. If you deceive here to deliberately waste the time of the developers, you will be permanently blocked.
      options:
        - label: I confirm that I have read the documentation, understand the meaning of all the configuration items I wrote, and did not pile up seemingly useful options or default values.
          required: true
        - label: I confirm that I have provided the server and client configuration files and process that can be reproduced locally, instead of a complicated client configuration file that has been stripped of sensitive data.
          required: true
        - label: I confirm that I have provided the simplest configuration that can be used to reproduce the error I reported, instead of depending on remote servers, TUN, graphical interface clients, or other closed-source software.
          required: true
        - label: I confirm that I have provided the complete configuration files and logs, rather than just providing parts I think are useful out of confidence in my own intelligence.
          required: true