project_name: sing-box
builds:
  - id: main
    main: ./cmd/sing-box
    flags:
      - -v
      - -trimpath
    ldflags:
      - -X github.com/sagernet/sing-box/constant.Version={{ .Version }}
      - -s
      - -buildid=
    tags:
      - with_gvisor
      - with_quic
      - with_dhcp
      - with_wireguard
      - with_utls
      - with_acme
      - with_clash_api
      - with_tailscale
    env:
      - CGO_ENABLED=0
    targets:
      - linux_386
      - linux_amd64_v1
      - linux_arm64
      - linux_arm_7
      - linux_s390x
      - linux_riscv64
      - linux_mips64le
    mod_timestamp: '{{ .CommitTimestamp }}'
snapshot:
  name_template: "{{ .Version }}.{{ .ShortCommit }}"
nfpms:
  - &template
    id: package
    package_name: sing-box
    file_name_template: '{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ with .Arm }}v{{ . }}{{ end }}{{ with .Mips }}_{{ . }}{{ end }}{{ if not (eq .Amd64 "v1") }}{{ .Amd64 }}{{ end }}'
    builds:
      - main
    homepage: https://sing-box.sagernet.org/
    maintainer: nekohasekai <<EMAIL>>
    description: The universal proxy platform.
    license: GPLv3 or later
    formats:
      - deb
      - rpm
    priority: extra
    contents:
      - src: release/config/config.json
        dst: /etc/sing-box/config.json
        type: "config|noreplace"

      - src: release/config/sing-box.service
        dst: /usr/lib/systemd/system/sing-box.service
      - src: release/config/sing-box@.service
        dst: /usr/lib/systemd/system/sing-box@.service
      - src: release/config/sing-box.sysusers
        dst: /usr/lib/sysusers.d/sing-box.conf
      - src: release/config/sing-box.rules
        dst: /usr/share/polkit-1/rules.d/sing-box.rules
      - src: release/config/sing-box-split-dns.xml
        dst: /usr/share/dbus-1/system.d/sing-box-split-dns.conf

      - src: release/completions/sing-box.bash
        dst: /usr/share/bash-completion/completions/sing-box.bash
      - src: release/completions/sing-box.fish
        dst: /usr/share/fish/vendor_completions.d/sing-box.fish
      - src: release/completions/sing-box.zsh
        dst: /usr/share/zsh/site-functions/_sing-box

      - src: LICENSE
        dst: /usr/share/licenses/sing-box/LICENSE
    deb:
      signature:
        key_file: "{{ .Env.NFPM_KEY_PATH }}"
      fields:
        Bugs: https://github.com/SagerNet/sing-box/issues
    rpm:
      signature:
        key_file: "{{ .Env.NFPM_KEY_PATH }}"
    conflicts:
      - sing-box-beta
  - id: package_beta
    <<: *template
    package_name: sing-box-beta
    file_name_template: '{{ .ProjectName }}-beta_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ with .Arm }}v{{ . }}{{ end }}{{ with .Mips }}_{{ . }}{{ end }}{{ if not (eq .Amd64 "v1") }}{{ .Amd64 }}{{ end }}'
    formats:
      - deb
      - rpm
    conflicts:
      - sing-box
release:
  disable: true
furies:
  - account: sagernet
    ids:
      - package
    disable: "{{ not (not .Prerelease) }}"
  - account: sagernet
    ids:
      - package_beta
    disable: "{{ not .Prerelease }}"
