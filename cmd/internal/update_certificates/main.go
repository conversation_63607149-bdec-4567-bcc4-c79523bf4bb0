package main

import (
	"encoding/csv"
	"io"
	"net/http"
	"os"
	"strings"

	"github.com/sagernet/sing-box/log"

	"golang.org/x/exp/slices"
)

func main() {
	err := updateMozillaIncludedRootCAs()
	if err != nil {
		log.Error(err)
	}
}

func updateMozillaIncludedRootCAs() error {
	response, err := http.Get("https://ccadb.my.salesforce-sites.com/mozilla/IncludedCACertificateReportPEMCSV")
	if err != nil {
		return err
	}
	defer response.Body.Close()
	reader := csv.NewReader(response.Body)
	header, err := reader.Read()
	if err != nil {
		return err
	}
	geoIndex := slices.Index(header, "Geographic Focus")
	nameIndex := slices.Index(header, "Common Name or Certificate Name")
	certIndex := slices.Index(header, "PEM Info")

	generated := strings.Builder{}
	generated.WriteString(`// Code generated by 'make update_certificates'. DO NOT EDIT.

package certificate

import "crypto/x509"

var mozillaIncluded *x509.CertPool

func init() {
	mozillaIncluded = x509.NewCertPool()
`)
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		} else if err != nil {
			return err
		}
		if record[geoIndex] == "China" {
			continue
		}
		generated.WriteString("\n	// ")
		generated.WriteString(record[nameIndex])
		generated.WriteString("\n")
		generated.WriteString("	mozillaIncluded.AppendCertsFromPEM([]byte(`")
		cert := record[certIndex]
		// Remove single quotes
		cert = cert[1 : len(cert)-1]
		generated.WriteString(cert)
		generated.WriteString("`))\n")
	}
	generated.WriteString("}\n")
	return os.WriteFile("common/certificate/mozilla.go", []byte(generated.String()), 0o644)
}
