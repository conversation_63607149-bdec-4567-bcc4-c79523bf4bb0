#!/bin/bash

echo "🔬 DoH域名解析时间影响测试"
echo "================================"

# 测试阿里DoH的域名解析时间影响
DOH_HOST="dns.alidns.com"
DOMAIN="google.com"

echo "🎯 测试目标: $DOH_HOST"
echo "🌐 查询域名: $DOMAIN"
echo ""

echo "📊 测试1: 包含域名解析时间（当前方法）"
echo "----------------------------------------"
for i in {1..3}; do
    echo -n "第${i}次: "
    time curl -s -w "%{time_total}s" \
        -H "Accept: application/dns-json" \
        "https://$DOH_HOST/dns-query?name=$DOMAIN&type=A" \
        --connect-timeout 5 --max-time 10 > /dev/null
    echo ""
done

echo ""
echo "📊 测试2: 预热后测试（扣除域名解析时间）"
echo "--------------------------------------------"

# 预热：建立连接和DNS缓存
echo "🔥 预热中..."
curl -s -I --connect-timeout 3 --max-time 5 "https://$DOH_HOST" > /dev/null 2>&1

echo "✅ 预热完成，开始测试:"
for i in {1..3}; do
    echo -n "第${i}次: "
    time curl -s -w "%{time_total}s" \
        -H "Accept: application/dns-json" \
        "https://$DOH_HOST/dns-query?name=$DOMAIN&type=A" \
        --connect-timeout 5 --max-time 10 > /dev/null
    echo ""
done

echo ""
echo "📊 测试3: 连续测试（模拟长期使用）"
echo "------------------------------------"
for i in {1..5}; do
    echo -n "第${i}次: "
    time curl -s -w "%{time_total}s" \
        -H "Accept: application/dns-json" \
        "https://$DOH_HOST/dns-query?name=$DOMAIN&type=A" \
        --connect-timeout 5 --max-time 10 > /dev/null
    echo ""
done

echo ""
echo "🔍 分析结论:"
echo "1. 第一次测试包含域名解析时间（较慢）"
echo "2. 预热后测试更接近真实长期使用性能"
echo "3. 连续测试显示缓存效果"
echo "4. 修复后的程序应该使用预热方法"
