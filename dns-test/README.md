# DNS 性能测试工具

基于您的 Clash 配置文件中的**所有 DNS 服务器**，进行全面性能测试，为 GUI.for.SingBox 生成最优配置。

## 🎯 测试规模

- **DNS 服务器**: 24个（涵盖配置文件中的所有DNS）
- **测试域名**: 9个（5个国际 + 4个国内）
- **测试类型**: UDP、DoH、DoT 全覆盖
- **总查询数**: 服务器数 × 测试次数 × 域名数

## 📋 完整 DNS 服务器列表

### 来自您的配置文件：
- **default-nameserver**: 223.5.5.5, 119.29.29.29, 114.114.114.114
- **nameserver**: dns.adguard-dns.com, public.dns.iij.jp, 1.1.1.1, 1.0.0.1  
- **fallback**: 9.9.9.9, doh.opendns.com
- **proxy-server-nameserver**: dns.alidns.com, 223.5.5.5, 120.53.53.53, 1.12.12.12
- **nameserver-policy**: 223.5.5.5, 119.29.29.29, 114.114.114.114

### 额外重要服务器：
- Google DNS/DoH, Cloudflare DNS/DoH, 百度DNS, 360DNS, OpenDNS, Quad9

## 🌐 测试域名

**国际域名 (5个)**:
- google.com, youtube.com, facebook.com, github.com, stackoverflow.com

**国内域名 (4个)**:
- baidu.com, qq.com, taobao.com, bilibili.com

## ⚙️ 代理模式选择

### 🎯 推荐：mihomo 普通代理模式（规则模式）

**为什么选择普通代理模式？**
- ✅ **真实场景**: DNS查询会根据规则分流，国内域名直连，国外域名走代理
- ✅ **准确测试**: 能测试出各DNS服务器在实际使用环境下的真实性能
- ✅ **分流效果**: 可以验证国内外DNS服务器的分工效果

### ❌ 不推荐：全局代理模式
- ❌ **结果失真**: 所有DNS查询都走代理，无法测试国内DNS的直连性能
- ❌ **不符实际**: 实际使用中很少全局代理DNS查询

### ❌ 绝对禁止：DNS劫持模式
- ❌ **无意义**: DNS查询被劫持，测试的是sing-box内部处理，不是外部DNS性能

## 🚀 使用方法

### 1. 准备环境
```bash
# 确保切换到 mihomo 普通代理模式
# 检查代理规则是否正常工作
```

### 2. 运行测试

#### 基础DNS测试（UDP）
```bash
# 给脚本执行权限
chmod +x run.sh test.sh

# 快速验证（3次测试）
./test.sh

# 完整测试（推荐50-100次）
./run.sh 50    # 50次测试
./run.sh 100   # 100次测试（更准确）
```

#### DoH/DoT专项测试（解决安全DNS问题）
```bash
# 给脚本执行权限
chmod +x test_secure_dns.sh

# DoH/DoT专项测试（推荐20-50次）
./test_secure_dns.sh 20   # 20次测试
./test_secure_dns.sh 50   # 50次测试（更准确）
```

### 3. 查看结果

#### 基础DNS测试结果
- `dns_test_report.csv` - Excel可读的测试报告
- `dns_test_stats.json` - 详细统计数据
- `recommended_dns_config.json` - 推荐的sing-box配置
- `DNS_CONFIG_README.md` - GUI.for.SingBox配置说明

#### DoH/DoT专项测试结果
- `secure_dns_stats.json` - 安全DNS详细统计
- `secure_dns_recommendation.md` - 安全DNS推荐配置

## 📊 测试指标

- **成功率**: DNS查询成功的百分比
- **平均延迟**: 所有成功查询的平均响应时间
- **最小/最大延迟**: 响应时间的范围
- **标准差**: 延迟的稳定性指标（越小越稳定）

## 🎯 GUI.for.SingBox 配置

测试完成后，程序会自动分析结果并推荐最优的4个DNS字段：

- **Local DNS**: 最佳国内DNS服务器
- **Local DNS Resolver**: 国内DNS解析器（用于解析Local DNS的域名）
- **Remote DNS**: 最佳国外DNS服务器  
- **Remote DNS Resolver**: 国外DNS解析器（用于解析Remote DNS的域名）

## 🔧 系统要求

- **操作系统**: macOS
- **必需工具**: 
  - Go 编译器
  - dig 命令（DNS查询）
  - curl 命令（HTTP请求）
- **可选工具**:
  - kdig 命令（更准确的DoT测试）

## 💡 注意事项

1. **测试时间**: 完整测试可能需要10-30分钟，请耐心等待
2. **网络稳定**: 确保网络连接稳定，避免影响测试结果
3. **DNS限制**: 某些DNS服务器可能有查询频率限制
4. **结果解读**: 优先选择成功率高且延迟低的DNS服务器
5. **多次测试**: 建议在不同时间段多次测试，取平均值

## 🔍 故障排除

### 编译失败
```bash
# 检查Go版本
go version

# 重新编译
go build -o dns-tester main.go
```

### 工具缺失
```bash
# 安装必需工具
brew install bind      # 安装 dig
brew install knot      # 安装 kdig (可选)
```

### 测试失败率高
- 检查网络连接
- 确认代理模式设置
- 尝试减少并发数（修改代码中的semaphore值）
