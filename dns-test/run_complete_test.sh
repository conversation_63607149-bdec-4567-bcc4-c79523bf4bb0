#!/bin/bash

# 完整 DNS 测试脚本（包含 UDP + DoH + DoT）
echo "🧪 完整 DNS 性能测试工具"
echo "========================="
echo "📋 测试内容:"
echo "  - 22个去重DNS服务器（UDP + DoH + DoT）"
echo "  - 9个测试域名（5个国际 + 4个国内）"
echo "  - 修复了DoH测试问题"
echo "  - 使用resolver解决域名解析"
echo ""

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "❌ 未找到 Go 编译器，请先安装 Go"
    exit 1
fi

# 检查系统工具
echo "🔧 检查系统工具..."
missing_tools=()

for tool in dig curl; do
    if command -v "$tool" &> /dev/null; then
        echo "✅ $tool 可用"
    else
        echo "❌ $tool 未找到"
        missing_tools+=("$tool")
    fi
done

# 检查kdig（DoT测试关键）
if command -v kdig &> /dev/null; then
    echo "✅ kdig 可用 (DoT测试将更准确)"
else
    echo "⚠️  kdig 未找到 (DoT测试将使用备用方法)"
    echo "💡 建议安装: brew install knot"
fi

if [ ${#missing_tools[@]} -ne 0 ]; then
    echo ""
    echo "❌ 缺少必要工具: ${missing_tools[*]}"
    echo "💡 安装命令:"
    echo "   brew install bind  # 安装 dig"
    exit 1
fi

# 获取测试次数
TEST_COUNT=${1:-50}
echo ""
echo "📊 测试参数:"
echo "  - 每个DNS服务器测试次数: $TEST_COUNT"
echo "  - 总查询数: 22 × $TEST_COUNT × 9 = $((22 * TEST_COUNT * 9))"
echo "  - 预计耗时: $((TEST_COUNT / 10))-$((TEST_COUNT / 5)) 分钟"
echo ""

# 提醒代理模式
echo "⚠️  重要提醒:"
echo "   请确保当前处于 mihomo 普通代理模式（规则模式）"
echo "   避免使用全局代理模式或DNS劫持模式"
echo ""

read -p "确认环境正确，开始测试? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "测试已取消"
    exit 0
fi

# 清理旧结果
echo "🧹 清理旧测试结果..."
rm -f complete_dns_*.json complete_dns_*.csv final_dns_config.json complete-dns-tester

# 编译程序
echo "🔨 编译完整测试程序..."
if ! go build -o complete-dns-tester complete_dns_tester.go; then
    echo "❌ 编译失败，请检查代码"
    exit 1
fi

# 检查可执行文件
if [ ! -f "./complete-dns-tester" ]; then
    echo "❌ 可执行文件未生成"
    exit 1
fi

echo "✅ 编译成功"
echo ""

# 运行测试
echo "🚀 开始完整DNS测试..."
echo "⏱️  这可能需要较长时间，请耐心等待..."
echo ""

./complete-dns-tester "$TEST_COUNT"

# 检查结果
if [ -f "complete_dns_report.csv" ]; then
    echo ""
    echo "✅ 完整测试成功完成！"
    echo ""
    echo "📋 快速查看结果 (前15名):"
    echo "========================================"
    
    # 显示前15行结果，格式化输出
    head -16 complete_dns_report.csv | column -t -s ',' | head -16
    
    echo ""
    echo "📁 生成的文件:"
    echo "  - complete_dns_report.csv (Excel可读的完整报告)"
    echo "  - complete_dns_stats.json (详细统计数据)"
    echo "  - final_dns_config.json (最终推荐配置)"
    
    echo ""
    echo "💡 下一步操作:"
    echo "  1. 用 Excel 打开 complete_dns_report.csv 查看详细结果"
    echo "  2. 查看控制台输出的推荐配置"
    echo "  3. 将推荐配置应用到 GUI.for.SingBox"
    
    echo ""
    echo "🎯 关键发现:"
    
    # 统计各类型DNS的可用性
    local_count=$(grep ",local," complete_dns_report.csv | grep -v ",0.00," | wc -l)
    remote_count=$(grep ",remote," complete_dns_report.csv | grep -v ",0.00," | wc -l)
    udp_count=$(grep ",udp," complete_dns_report.csv | grep -v ",0.00," | wc -l)
    doh_count=$(grep ",doh," complete_dns_report.csv | grep -v ",0.00," | wc -l)
    dot_count=$(grep ",dot," complete_dns_report.csv | grep -v ",0.00," | wc -l)
    
    echo "  - 可用本地DNS: $local_count 个"
    echo "  - 可用远程DNS: $remote_count 个"
    echo "  - 可用UDP DNS: $udp_count 个"
    echo "  - 可用DoH DNS: $doh_count 个"
    echo "  - 可用DoT DNS: $dot_count 个"
    
else
    echo "❌ 测试失败，未找到结果文件"
    echo "请检查:"
    echo "  1. 网络连接是否正常"
    echo "  2. 代理模式是否正确"
    echo "  3. 系统工具是否完整"
    exit 1
fi

# 清理编译文件
rm -f complete-dns-tester

echo ""
echo "🎉 完整DNS测试完成！"
echo "📖 查看生成的文件了解详细结果和推荐配置"
