#!/bin/bash

echo "🔧 IIJ DoH 专项测试"
echo "==================="

# 测试域名
DOMAIN="google.com"
IIJ_HOST="public.dns.iij.jp"

echo "🌐 测试域名: $DOMAIN"
echo "🏢 IIJ DoH: $IIJ_HOST"
echo ""

# 方法1: 标准JSON格式
echo "📊 方法1: JSON格式 (/dns-query)"
curl -s -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -H "Accept: application/dns-json" \
  "https://$IIJ_HOST/dns-query?name=$DOMAIN&type=A" \
  --connect-timeout 5 --max-time 10
echo ""

# 方法2: 尝试不同路径
echo "📊 方法2: JSON格式 (/resolve)"
curl -s -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -H "Accept: application/dns-json" \
  "https://$IIJ_HOST/resolve?name=$DOMAIN&type=A" \
  --connect-timeout 5 --max-time 10
echo ""

# 方法3: Wire Format
echo "🔧 方法3: Wire Format (/dns-query)"
curl -s -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -H "Accept: application/dns-message" \
  -H "Content-Type: application/dns-message" \
  "https://$IIJ_HOST/dns-query" \
  --connect-timeout 5 --max-time 10
echo ""

# 方法4: 基本连接测试
echo "🌐 方法4: 基本连接测试"
curl -I -s -w "HTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  "https://$IIJ_HOST/dns-query" \
  --connect-timeout 5 --max-time 10
echo ""

# 方法5: 使用cloudflared测试（如果可用）
echo "☁️  方法5: cloudflared测试（如果可用）"
if command -v cloudflared &> /dev/null; then
    echo "✅ cloudflared 可用，测试中..."
    timeout 10 cloudflared proxy-dns --upstream https://$IIJ_HOST/dns-query --port 5555 &
    CLOUDFLARED_PID=$!
    sleep 2
    
    # 使用本地代理测试
    dig @127.0.0.1 -p 5555 $DOMAIN A +short
    
    # 清理
    kill $CLOUDFLARED_PID 2>/dev/null
else
    echo "❌ cloudflared 未安装"
    echo "💡 安装: brew install cloudflare/cloudflare/cloudflared"
fi
echo ""

# 方法6: 使用kdig测试DoH（如果支持）
echo "🔧 方法6: kdig DoH测试（如果支持）"
if command -v kdig &> /dev/null; then
    echo "✅ kdig 可用，测试中..."
    kdig @$IIJ_HOST $DOMAIN A +https +time=5 +short
else
    echo "❌ kdig 未安装"
    echo "💡 安装: brew install knot"
fi
echo ""

echo "🎯 测试完成！"
echo "如果所有方法都失败，可能是："
echo "1. IIJ DoH 服务暂时不可用"
echo "2. 需要特殊的认证或配置"
echo "3. 网络连接问题"
