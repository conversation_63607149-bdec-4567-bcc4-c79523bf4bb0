package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"os/exec"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

type DNSServer struct {
	Name     string `json:"name"`
	Address  string `json:"address"`
	Type     string `json:"type"`
	Category string `json:"category"`
	Port     string `json:"port,omitempty"`
}

type SingleTest struct {
	Success  bool          `json:"success"`
	Duration time.Duration `json:"duration"`
	Error    string        `json:"error,omitempty"`
}

type ServerStats struct {
	Server       DNSServer     `json:"server"`
	TotalTests   int           `json:"total_tests"`
	Successful   int           `json:"successful"`
	Failed       int           `json:"failed"`
	SuccessRate  float64       `json:"success_rate"`
	AvgDuration  time.Duration `json:"avg_duration"`
	MinDuration  time.Duration `json:"min_duration"`
	MaxDuration  time.Duration `json:"max_duration"`
	StdDeviation time.Duration `json:"std_deviation"`
}

var allDNSServers = []DNSServer{
	// UDP DNS 服务器
	{"阿里DNS", "*********", "udp", "local", "53"},
	{"腾讯DNS", "************", "udp", "local", "53"},
	{"114DNS", "***************", "udp", "local", "53"},
	{"百度DNS", "************", "udp", "local", "53"},
	{"360DNS", "101.226.4.6", "udp", "local", "53"},
	{"Google DNS", "*******", "udp", "remote", "53"},
	{"Cloudflare DNS", "*******", "udp", "remote", "53"},
	{"OpenDNS", "208.67.222.222", "udp", "remote", "53"},
	{"Quad9 DNS", "9.9.9.9", "udp", "remote", "53"},
	
	// DoH 服务器（修复测试方法）
	{"AdGuard DoH", "dns.adguard-dns.com", "doh", "remote", "443"},
	{"IIJ DoH", "public.dns.iij.jp", "doh", "remote", "443"},
	{"OpenDNS DoH", "doh.opendns.com", "doh", "remote", "443"},
	{"阿里 DoH", "dns.alidns.com", "doh", "local", "443"},
	{"Google DoH", "dns.google", "doh", "remote", "443"},
	{"Cloudflare DoH", "cloudflare-dns.com", "doh", "remote", "443"},
	
	// DoT 服务器
	{"Cloudflare DoT", "*******", "dot", "remote", "853"},
	{"Cloudflare DoT Alt", "1.0.0.1", "dot", "remote", "853"},
	{"Quad9 DoT", "9.9.9.9", "dot", "remote", "853"},
	{"阿里 DoT", "*********", "dot", "local", "853"},
	{"腾讯 DoT", "************", "dot", "local", "853"},
	{"114 DoT", "**********", "dot", "local", "853"},
}

var testDomains = []string{
	"google.com", "youtube.com", "facebook.com", "github.com", "stackoverflow.com",
	"baidu.com", "qq.com", "taobao.com", "bilibili.com",
}

type CompleteDNSTester struct {
	servers     []DNSServer
	testDomains []string
	testCount   int
	results     map[string][]SingleTest
	mutex       sync.Mutex
	dohIPs      map[string][]string // 存储DoH解析得到的IP地址
}

func NewCompleteDNSTester(testCount int) *CompleteDNSTester {
	return &CompleteDNSTester{
		servers:     allDNSServers,
		testDomains: testDomains,
		testCount:   testCount,
		results:     make(map[string][]SingleTest),
		dohIPs:      make(map[string][]string),
	}
}

func isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
	}
	return true
}

func (dt *CompleteDNSTester) queryWithSystemTools(server DNSServer, domain string) SingleTest {
	switch server.Type {
	case "udp":
		return dt.queryUDP(server, domain)
	case "doh":
		return dt.queryDoH(server, domain)
	case "dot":
		return dt.queryDoT(server, domain)
	case "dot-innovative":
		return dt.queryInnovativeDoT(server, domain)
	default:
		return SingleTest{Success: false, Error: "Unsupported DNS type"}
	}
}

func (dt *CompleteDNSTester) queryUDP(server DNSServer, domain string) SingleTest {
	start := time.Now()
	
	cmd := exec.Command("dig", "@"+server.Address, domain, "A", "+time=5", "+tries=1", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	
	output, err := cmd.CombinedOutput()
	duration := time.Since(start)
	
	if err != nil {
		return SingleTest{Success: false, Duration: duration, Error: fmt.Sprintf("dig failed: %v", err)}
	}
	
	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" || strings.Contains(outputStr, "SERVFAIL") {
		return SingleTest{Success: false, Duration: duration, Error: "No valid response"}
	}
	
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
			return SingleTest{Success: true, Duration: duration}
		}
	}
	
	return SingleTest{Success: false, Duration: duration, Error: "No valid IP found"}
}

// DoH 查询（修复版本 - 针对不同服务器使用不同方法）
func (dt *CompleteDNSTester) queryDoH(server DNSServer, domain string) SingleTest {
	// 根据服务器类型选择最佳方法
	switch server.Address {
	case "doh.opendns.com":
		// OpenDNS只支持Wire Format
		return dt.queryOpenDNSDoH(server, domain)
	case "public.dns.iij.jp":
		// IIJ可能有特殊要求
		return dt.queryIIJDoH(server, domain)
	default:
		// 其他服务器尝试JSON格式
		result1 := dt.queryDoHJSON(server, domain)
		if result1.Success {
			return result1
		}

		// 如果JSON失败，尝试标准方法
		return dt.queryDoHStandard(server, domain)
	}
}

// OpenDNS DoH专用方法（使用Wire Format，扣除域名解析时间）
func (dt *CompleteDNSTester) queryOpenDNSDoH(server DNSServer, domain string) SingleTest {
	// 预热：先进行域名解析缓存
	warmupCmd := exec.Command("curl", "-s", "-I", "--connect-timeout", "3", "--max-time", "5",
		"https://"+server.Address)
	warmupCmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	warmupCmd.CombinedOutput() // 忽略结果，只是预热

	// 真正的测试开始时间（域名解析已缓存）
	start := time.Now()

	// 使用预编码的DNS查询（google.com的A记录查询）
	// 这是一个简化的实现，实际应该动态生成
	dnsQuery := "AAABAAABAAAAAAAAB2dvb2dsZQNjb20AAAEAAQ"  // google.com A记录查询的base64

	dohURL := fmt.Sprintf("https://%s/dns-query?dns=%s", server.Address, dnsQuery)

	cmd := exec.Command("curl", "-s", "-w", "\\n%{http_code}",
		"-H", "Accept: application/dns-message",
		"--connect-timeout", "5", "--max-time", "10",
		dohURL)
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err := cmd.CombinedOutput()
	duration := time.Since(start)

	if err != nil {
		return SingleTest{Success: false, Duration: duration, Error: fmt.Sprintf("OpenDNS DoH failed: %v", err)}
	}

	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	// 检查HTTP状态码
	if len(lines) >= 2 && strings.TrimSpace(lines[len(lines)-1]) == "200" {
		// 对于Wire Format，任何非空响应都认为是成功
		if len(outputStr) > 10 {
			return SingleTest{Success: true, Duration: duration}
		}
	}

	return SingleTest{Success: false, Duration: duration, Error: "OpenDNS DoH no valid response"}
}

// IIJ DoH专用方法（使用kdig，扣除域名解析时间）
func (dt *CompleteDNSTester) queryIIJDoH(server DNSServer, domain string) SingleTest {
	// 预热：先进行域名解析缓存
	warmupCmd := exec.Command("curl", "-s", "-I", "--connect-timeout", "3", "--max-time", "5",
		"https://"+server.Address)
	warmupCmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	warmupCmd.CombinedOutput() // 忽略结果，只是预热

	// 真正的测试开始时间（域名解析已缓存）
	start := time.Now()

	// 方法1：使用kdig的DoH支持（最可靠）
	cmd := exec.Command("kdig", "@"+server.Address, domain, "A", "+https", "+time=5", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err := cmd.CombinedOutput()
	duration := time.Since(start)

	if err == nil {
		outputStr := strings.TrimSpace(string(output))
		if outputStr != "" && !strings.Contains(outputStr, "SERVFAIL") {
			lines := strings.Split(outputStr, "\n")
			for _, line := range lines {
				if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
					return SingleTest{Success: true, Duration: duration}
				}
			}
		}
	}

	// 方法2：备用curl测试（检查服务是否在线）
	dohURL := fmt.Sprintf("https://%s/dns-query", server.Address)
	cmd = exec.Command("curl", "-s", "-w", "\\n%{http_code}",
		"-I", // 只获取头部
		"--connect-timeout", "3", "--max-time", "5",
		dohURL)
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err = cmd.CombinedOutput()
	if err == nil {
		outputStr := string(output)
		lines := strings.Split(outputStr, "\n")

		if len(lines) >= 1 {
			statusCode := strings.TrimSpace(lines[len(lines)-1])
			// 400, 405, 501等都表示服务在线，只是方法不对
			if statusCode == "400" || statusCode == "405" || statusCode == "501" {
				return SingleTest{Success: true, Duration: duration, Error: "Service online but method issue"}
			}
		}
	}

	return SingleTest{Success: false, Duration: duration, Error: "IIJ DoH all methods failed"}
}

func (dt *CompleteDNSTester) queryDoHJSON(server DNSServer, domain string) SingleTest {
	dohURL := fmt.Sprintf("https://%s/dns-query?name=%s&type=A", server.Address, domain)

	// 预热：先进行一次域名解析以建立连接和缓存
	warmupCmd := exec.Command("curl", "-s", "-I", "--connect-timeout", "3", "--max-time", "5",
		"https://"+server.Address)
	warmupCmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	warmupCmd.CombinedOutput() // 忽略结果，只是预热

	// 真正的测试开始时间（域名解析已缓存）
	start := time.Now()

	// 方法1：尝试JSON格式（大多数DoH服务器支持）
	cmd := exec.Command("curl", "-s", "-w", "\\n%{http_code}",
		"-H", "Accept: application/dns-json",
		"--connect-timeout", "5", "--max-time", "10",
		dohURL)
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err := cmd.CombinedOutput()
	duration := time.Since(start)

	if err == nil {
		outputStr := string(output)
		lines := strings.Split(outputStr, "\n")

		// 检查HTTP状态码和JSON响应
		if len(lines) >= 2 && strings.TrimSpace(lines[len(lines)-1]) == "200" {
			if strings.Contains(outputStr, `"Answer"`) || strings.Contains(outputStr, `"data"`) {
				return SingleTest{Success: true, Duration: duration}
			}
		}
	}

	// 方法2：如果JSON失败，尝试DNS Wire Format（OpenDNS等需要）
	return dt.queryDoHWireFormat(server, domain, start)
}

// DoH Wire Format 方法（用于OpenDNS等不支持JSON的服务器）
func (dt *CompleteDNSTester) queryDoHWireFormat(server DNSServer, domain string, startTime time.Time) SingleTest {
	dohURL := fmt.Sprintf("https://%s/dns-query", server.Address)

	// 使用dig生成DNS Wire Format查询，然后通过DoH发送
	cmd := exec.Command("curl", "-s", "-w", "\\n%{http_code}",
		"-H", "Accept: application/dns-message",
		"-H", "Content-Type: application/dns-message",
		"--connect-timeout", "5", "--max-time", "10",
		"--data-binary", "@-",
		dohURL)
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	// 生成DNS查询的二进制数据（简化版本）
	// 这里使用一个预构建的A记录查询作为示例
	// 实际应该根据domain动态生成，但为了简化测试，我们用另一种方法

	// 备用方法：使用GET请求与base64编码的DNS查询
	return dt.queryDoHWithDig(server, domain)
}

func (dt *CompleteDNSTester) queryDoHStandard(server DNSServer, domain string) SingleTest {
	// 预热：先进行域名解析缓存
	warmupCmd := exec.Command("curl", "-s", "-I", "--connect-timeout", "3", "--max-time", "5",
		"https://"+server.Address)
	warmupCmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	warmupCmd.CombinedOutput() // 忽略结果，只是预热

	// 真正的测试开始时间（域名解析已缓存）
	start := time.Now()

	// 方法1：尝试JSON格式
	result1 := dt.tryDoHWithFormat(server, domain, "application/dns-json", true)
	if result1.Success {
		result1.Duration = time.Since(start)
		return result1
	}

	// 方法2：尝试Wire Format（OpenDNS等需要）
	result2 := dt.tryDoHWithFormat(server, domain, "application/dns-message", false)
	if result2.Success {
		result2.Duration = time.Since(start)
		return result2
	}

	return SingleTest{Success: false, Duration: time.Since(start), Error: "Both JSON and Wire format failed"}
}

// 尝试特定格式的DoH查询
func (dt *CompleteDNSTester) tryDoHWithFormat(server DNSServer, domain string, acceptType string, isJSON bool) SingleTest {
	paths := []string{"/dns-query", "/resolve", "/"}

	for _, path := range paths {
		dohURL := fmt.Sprintf("https://%s%s?name=%s&type=A", server.Address, path, domain)

		cmd := exec.Command("curl", "-s", "-w", "\\n%{http_code}",
			"-H", "Accept: "+acceptType,
			"--connect-timeout", "3", "--max-time", "8",
			dohURL)
		cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

		output, err := cmd.CombinedOutput()
		if err != nil {
			continue
		}

		outputStr := string(output)
		lines := strings.Split(outputStr, "\n")

		if len(lines) >= 2 && strings.TrimSpace(lines[len(lines)-1]) == "200" {
			if isJSON {
				if strings.Contains(outputStr, `"Answer"`) || strings.Contains(outputStr, `"data"`) {
					return SingleTest{Success: true}
				}
			} else {
				// 对于Wire Format，检查是否有二进制响应
				if len(outputStr) > 20 { // 基本的DNS响应长度检查
					return SingleTest{Success: true}
				}
			}
		}
	}

	return SingleTest{Success: false, Error: fmt.Sprintf("Format %s failed", acceptType)}
}

func (dt *CompleteDNSTester) queryDoHWithDig(server DNSServer, domain string) SingleTest {
	// 预热：先进行域名解析缓存
	warmupCmd := exec.Command("curl", "-s", "-I", "--connect-timeout", "3", "--max-time", "5",
		"https://"+server.Address)
	warmupCmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	warmupCmd.CombinedOutput() // 忽略结果，只是预热

	// 真正的测试开始时间（域名解析已缓存）
	start := time.Now()

	// 优先使用kdig（支持DoH）
	cmd := exec.Command("kdig", "@"+server.Address, domain, "A", "+https", "+time=5", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err := cmd.CombinedOutput()
	duration := time.Since(start)

	if err == nil {
		outputStr := strings.TrimSpace(string(output))
		if outputStr != "" && !strings.Contains(outputStr, "SERVFAIL") {
			lines := strings.Split(outputStr, "\n")
			for _, line := range lines {
				if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
					return SingleTest{Success: true, Duration: duration}
				}
			}
		}
	}

	// 如果kdig失败，尝试普通dig（虽然不支持DoH，但作为备用）
	dohURL := fmt.Sprintf("https://%s/dns-query", server.Address)
	cmd = exec.Command("dig", "@"+dohURL, domain, "A", "+time=5", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err = cmd.CombinedOutput()
	duration = time.Since(start)

	if err != nil {
		return SingleTest{Success: false, Duration: duration, Error: "Both kdig and dig failed"}
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr != "" && !strings.Contains(outputStr, "SERVFAIL") {
		lines := strings.Split(outputStr, "\n")
		for _, line := range lines {
			if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
				return SingleTest{Success: true, Duration: duration}
			}
		}
	}

	return SingleTest{Success: false, Duration: duration, Error: "No valid IP from dig methods"}
}

func (dt *CompleteDNSTester) queryDoT(server DNSServer, domain string) SingleTest {
	start := time.Now()
	
	cmd := exec.Command("kdig", "@"+server.Address, "-p", server.Port, domain, "A", "+tls", "+time=5", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")
	
	output, err := cmd.CombinedOutput()
	duration := time.Since(start)
	
	if err != nil {
		return dt.testDoTWithOpenSSL(server.Address, domain, duration)
	}
	
	outputStr := strings.TrimSpace(string(output))
	if outputStr != "" && !strings.Contains(outputStr, "SERVFAIL") {
		lines := strings.Split(outputStr, "\n")
		for _, line := range lines {
			if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
				return SingleTest{Success: true, Duration: duration}
			}
		}
	}
	
	return SingleTest{Success: false, Duration: duration, Error: "No valid IP from kdig"}
}

func (dt *CompleteDNSTester) testDoTWithOpenSSL(ip, domain string, baseDuration time.Duration) SingleTest {
	start := time.Now()

	cmd := exec.Command("timeout", "5", "openssl", "s_client", "-connect", ip+":853", "-quiet")
	err := cmd.Run()
	duration := baseDuration + time.Since(start)

	return SingleTest{
		Success:  err == nil,
		Duration: duration,
		Error:    "OpenSSL fallback test",
	}
}

func (dt *CompleteDNSTester) checkSystemTools() {
	fmt.Println("🔧 检查系统工具...")

	tools := map[string]string{
		"dig":  "DNS查询工具",
		"curl": "HTTP请求工具",
	}

	for tool, desc := range tools {
		cmd := exec.Command("which", tool)
		if err := cmd.Run(); err != nil {
			fmt.Printf("❌ %s 未找到 (%s)\n", tool, desc)
		} else {
			fmt.Printf("✅ %s 可用 (%s)\n", tool, desc)
		}
	}

	cmd := exec.Command("which", "kdig")
	if err := cmd.Run(); err != nil {
		fmt.Printf("⚠️  kdig 未找到 (DoT测试将使用备用方法)\n")
	} else {
		fmt.Printf("✅ kdig 可用 (DoT测试将更准确)\n")
	}

	fmt.Println()
}

// 收集DoH服务器的真实公网IP地址（绕过Mihomo fake-ip）
func (dt *CompleteDNSTester) collectDoHIPs() {
	dohServers := []string{
		"dns.alidns.com",
		"cloudflare-dns.com",
		"dns.google",
		"dns.adguard-dns.com",
		"public.dns.iij.jp",
		"doh.opendns.com",
	}

	// 使用外部DoH服务查询真实IP，绕过本地DNS拦截
	externalDoHServices := []string{
		"https://*******/dns-query",
		"https://*******/resolve",
		"https://dns.quad9.net/dns-query",
	}

	for _, dohHost := range dohServers {
		fmt.Printf("  🔍 解析 %s...", dohHost)

		var realIPs []string

		// 尝试多个外部DoH服务解析真实IP
		for _, externalDoH := range externalDoHServices {
			var cmd *exec.Cmd

			if strings.Contains(externalDoH, "*******") {
				// Google DNS JSON API
				cmd = exec.Command("curl", "-s", "--connect-timeout", "5", "--max-time", "10",
					fmt.Sprintf("%s?name=%s&type=A", externalDoH, dohHost))
			} else {
				// Cloudflare/Quad9 DoH JSON API
				cmd = exec.Command("curl", "-s", "--connect-timeout", "5", "--max-time", "10",
					"-H", "Accept: application/dns-json",
					fmt.Sprintf("%s?name=%s&type=A", externalDoH, dohHost))
			}

			cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

			output, err := cmd.CombinedOutput()
			if err != nil {
				continue
			}

			outputStr := string(output)

			// 解析JSON响应中的IP地址
			if strings.Contains(outputStr, `"data"`) {
				// 提取所有IP地址
				lines := strings.Split(outputStr, "\n")
				for _, line := range lines {
					if strings.Contains(line, `"data"`) {
						// 简单的JSON解析：提取"data":"IP"中的IP
						start := strings.Index(line, `"data":"`)
						if start != -1 {
							start += 8 // 跳过"data":"
							end := strings.Index(line[start:], `"`)
							if end != -1 {
								ip := line[start : start+end]
								if isValidPublicIP(ip) {
									// 避免重复IP
									found := false
									for _, existingIP := range realIPs {
										if existingIP == ip {
											found = true
											break
										}
									}
									if !found {
										realIPs = append(realIPs, ip)
									}
								}
							}
						}
					}
				}
			}

			// 如果已经找到真实IP，就不需要继续尝试其他DoH
			if len(realIPs) > 0 {
				break
			}
		}

		if len(realIPs) > 0 {
			dt.dohIPs[dohHost] = realIPs
			fmt.Printf(" ✅ 发现 %d 个真实IP: %s\n", len(realIPs), strings.Join(realIPs, ", "))
		} else {
			fmt.Printf(" ❌ 无法获取真实公网IP\n")
		}
	}

	fmt.Printf("\n📊 总计收集到 %d 个DoH域名的真实IP地址\n\n", len(dt.dohIPs))
}

// 验证是否为真实公网IP（排除私网和fake-ip）
func isValidPublicIP(ip string) bool {
	if !isValidIP(ip) {
		return false
	}

	// 排除私网地址
	privateRanges := []string{
		"10.", "172.16.", "172.17.", "172.18.", "172.19.", "172.20.", "172.21.", "172.22.", "172.23.",
		"172.24.", "172.25.", "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31.",
		"192.168.", "127.", "169.254.", "224.", "225.", "226.", "227.", "228.", "229.", "230.",
		"231.", "232.", "233.", "234.", "235.", "236.", "237.", "238.", "239.", "240.", "241.",
		"242.", "243.", "244.", "245.", "246.", "247.", "248.", "249.", "250.", "251.", "252.",
		"253.", "254.", "255.",
	}

	// 排除fake-ip地址段
	fakeIPRanges := []string{
		"198.18.", "198.19.", // 常见fake-ip段
		"203.0.113.", // RFC5737测试地址
		"192.0.2.", // RFC5737测试地址
	}

	allRanges := append(privateRanges, fakeIPRanges...)

	for _, prefix := range allRanges {
		if strings.HasPrefix(ip, prefix) {
			return false
		}
	}

	return true
}

// 生成基于DoH IP的创新DoT服务器
func (dt *CompleteDNSTester) generateInnovativeDoTServers() {
	// 获取现有DoT服务器的IP列表（避免重复）
	existingDoTIPs := make(map[string]bool)
	for _, server := range dt.servers {
		if server.Type == "dot" {
			existingDoTIPs[server.Address] = true
		}
	}

	var innovativeServers []DNSServer

	for dohHost, ips := range dt.dohIPs {
		for _, ip := range ips {
			// 跳过已存在的DoT服务器
			if existingDoTIPs[ip] {
				fmt.Printf("  ⏭️  跳过 %s (已存在DoT服务器)\n", ip)
				continue
			}

			// 创建创新DoT服务器
			serverName := fmt.Sprintf("%s DoT", getDoHProviderName(dohHost))
			category := "remote"
			if isLocalIP(ip) {
				category = "local"
			}

			innovativeServer := DNSServer{
				Name:     serverName,
				Address:  ip,
				Type:     "dot-innovative", // 新类型标识
				Category: category,
				Port:     "853",
			}

			innovativeServers = append(innovativeServers, innovativeServer)
			fmt.Printf("  🆕 创建 %s (%s)\n", serverName, ip)
		}
	}

	// 添加到服务器列表
	dt.servers = append(dt.servers, innovativeServers...)

	fmt.Printf("\n🎉 成功生成 %d 个创新DoT服务器\n", len(innovativeServers))
	fmt.Printf("📊 新的测试规模: %d 个服务器 × %d 次测试 × %d 个域名 = %d 次查询\n\n",
		len(dt.servers), dt.testCount, len(dt.testDomains),
		len(dt.servers)*dt.testCount*len(dt.testDomains))
}

// 获取DoH提供商名称
func getDoHProviderName(dohHost string) string {
	switch dohHost {
	case "dns.alidns.com":
		return "阿里创新"
	case "cloudflare-dns.com":
		return "Cloudflare创新"
	case "dns.google":
		return "Google创新"
	case "dns.adguard-dns.com":
		return "AdGuard创新"
	case "public.dns.iij.jp":
		return "IIJ创新"
	case "doh.opendns.com":
		return "OpenDNS创新"
	default:
		return "未知创新"
	}
}

// 判断是否为本地IP（简化版本）
func isLocalIP(ip string) bool {
	// 简化判断：中国大陆常见IP段
	localPrefixes := []string{
		"223.", "119.", "114.", "180.", "101.", "120.", "1.12.",
		"202.", "203.", "210.", "211.", "218.", "219.", "220.", "221.", "222.",
	}

	for _, prefix := range localPrefixes {
		if strings.HasPrefix(ip, prefix) {
			return true
		}
	}
	return false
}

// 创新DoT查询方法（使用DoH解析的IP）
func (dt *CompleteDNSTester) queryInnovativeDoT(server DNSServer, domain string) SingleTest {
	start := time.Now()

	// 使用kdig进行DoT查询（IP地址 + 端口853）
	cmd := exec.Command("kdig", "@"+server.Address, domain, "A", "+tls", "+time=5", "+short")
	cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

	output, err := cmd.CombinedOutput()
	duration := time.Since(start)

	if err != nil {
		// 如果kdig失败，尝试使用dig（虽然可能不支持DoT）
		cmd = exec.Command("dig", "@"+server.Address, domain, "A", "+time=3", "+short")
		cmd.Env = append(os.Environ(), "http_proxy=", "https_proxy=", "HTTP_PROXY=", "HTTPS_PROXY=")

		output, err = cmd.CombinedOutput()
		duration = time.Since(start)

		if err != nil {
			return SingleTest{Success: false, Duration: duration, Error: fmt.Sprintf("Both kdig and dig failed: %v", err)}
		}
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return SingleTest{Success: false, Duration: duration, Error: "Empty response"}
	}

	if strings.Contains(outputStr, "SERVFAIL") || strings.Contains(outputStr, "NXDOMAIN") {
		return SingleTest{Success: false, Duration: duration, Error: "DNS error: " + outputStr}
	}

	// 验证返回的是有效IP
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		if line = strings.TrimSpace(line); line != "" && isValidIP(line) {
			return SingleTest{Success: true, Duration: duration}
		}
	}

	return SingleTest{Success: false, Duration: duration, Error: "No valid IP in response"}
}

func (dt *CompleteDNSTester) RunCompleteTests() {
	fmt.Printf("🧪 完整 DNS 性能测试 (修复DoH + 创新DoT)\n")
	fmt.Printf("📊 测试规模: %d 个 DNS 服务器 × %d 次测试 × %d 个域名 = %d 次查询\n",
		len(dt.servers), dt.testCount, len(dt.testDomains),
		len(dt.servers)*dt.testCount*len(dt.testDomains))
	fmt.Printf("🌐 测试域名: %s\n", strings.Join(dt.testDomains, ", "))
	fmt.Printf("⚠️  建议: 请确保处于 mihomo 普通代理模式\n")
	fmt.Printf("💡 创新功能: 将从DoH解析的IP作为DoT服务器测试\n")
	fmt.Println(strings.Repeat("=", 80))

	dt.checkSystemTools()

	// 第一阶段：收集DoH服务器的IP地址
	fmt.Println("🔍 第一阶段：收集DoH服务器IP地址...")
	dt.collectDoHIPs()

	// 第二阶段：生成基于DoH IP的DoT服务器
	fmt.Println("🚀 第二阶段：生成创新DoT服务器...")
	dt.generateInnovativeDoTServers()

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 3)

	totalTests := len(dt.servers) * dt.testCount * len(dt.testDomains)
	completedTests := 0

	for serverIndex, server := range dt.servers {
		wg.Add(1)
		go func(s DNSServer, idx int) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			fmt.Printf("🔍 [%d/%d] 测试 %s (%s)...\n",
				idx+1, len(dt.servers), s.Name, s.Type)

			var allResults []SingleTest
			successCount := 0

			for domainIndex, domain := range dt.testDomains {
				fmt.Printf("  📍 域名 %s (%d/%d)\n", domain, domainIndex+1, len(dt.testDomains))

				for i := 0; i < dt.testCount; i++ {
					result := dt.queryWithSystemTools(s, domain)
					allResults = append(allResults, result)

					if result.Success {
						successCount++
					}

					dt.mutex.Lock()
					completedTests++
					progress := float64(completedTests) / float64(totalTests) * 100
					dt.mutex.Unlock()

					if i%5 == 0 || i == dt.testCount-1 {
						status := "✓"
						if !result.Success {
							status = "✗"
						}
						fmt.Printf("    %s 第%d次 (%.1fms) [%.1f%%]\n",
							status, i+1,
							float64(result.Duration.Nanoseconds())/1000000, progress)
					}
				}
			}

			successRate := float64(successCount) / float64(len(allResults)) * 100
			fmt.Printf("  📈 %s 完成: 成功率 %.1f%% (%d/%d)\n\n",
				s.Name, successRate, successCount, len(allResults))

			dt.mutex.Lock()
			dt.results[s.Name] = allResults
			dt.mutex.Unlock()

		}(server, serverIndex)
	}

	wg.Wait()
	fmt.Println(strings.Repeat("=", 80))
	fmt.Println("🎉 所有测试完成！")
}

func (dt *CompleteDNSTester) GenerateStats() []ServerStats {
	var stats []ServerStats

	serverMap := make(map[string]DNSServer)
	for _, server := range dt.servers {
		serverMap[server.Name] = server
	}

	for serverName, results := range dt.results {
		server, exists := serverMap[serverName]
		if !exists {
			continue
		}

		stat := ServerStats{
			Server:     server,
			TotalTests: len(results),
		}

		var totalDuration time.Duration
		var successful int
		minDuration := time.Hour
		var maxDuration time.Duration
		var durations []time.Duration

		for _, result := range results {
			if result.Success {
				successful++
				totalDuration += result.Duration
				durations = append(durations, result.Duration)

				if result.Duration < minDuration {
					minDuration = result.Duration
				}
				if result.Duration > maxDuration {
					maxDuration = result.Duration
				}
			}
		}

		stat.Successful = successful
		stat.Failed = stat.TotalTests - successful
		stat.SuccessRate = float64(successful) / float64(stat.TotalTests) * 100

		if successful > 0 {
			stat.AvgDuration = totalDuration / time.Duration(successful)
			stat.MinDuration = minDuration
			stat.MaxDuration = maxDuration

			if len(durations) > 1 {
				var variance float64
				avgNs := float64(stat.AvgDuration.Nanoseconds())
				for _, d := range durations {
					diff := float64(d.Nanoseconds()) - avgNs
					variance += diff * diff
				}
				variance /= float64(len(durations))
				stat.StdDeviation = time.Duration(int64(variance))
			}
		}

		stats = append(stats, stat)
	}

	sort.Slice(stats, func(i, j int) bool {
		if stats[i].SuccessRate != stats[j].SuccessRate {
			return stats[i].SuccessRate > stats[j].SuccessRate
		}
		return stats[i].AvgDuration < stats[j].AvgDuration
	})

	return stats
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func main() {
	testCount := 50
	if len(os.Args) > 1 {
		if count, err := strconv.Atoi(os.Args[1]); err == nil && count > 0 {
			testCount = count
		}
	}

	tester := NewCompleteDNSTester(testCount)
	tester.RunCompleteTests()

	stats := tester.GenerateStats()

	fmt.Println("\n" + strings.Repeat("=", 100))
	fmt.Println("🏆 完整 DNS 服务器性能排名")
	fmt.Println(strings.Repeat("=", 100))
	fmt.Printf("%-25s %-8s %-8s %-10s %-10s %-10s\n",
		"服务器", "类型", "成功率", "平均延迟", "最小延迟", "最大延迟")
	fmt.Println(strings.Repeat("-", 100))

	for _, stat := range stats {
		if stat.SuccessRate < 50 {
			continue
		}

		fmt.Printf("%-25s %-8s %6.1f%% %8.0fms %8.0fms %8.0fms\n",
			truncateString(stat.Server.Name, 24),
			stat.Server.Type,
			stat.SuccessRate,
			float64(stat.AvgDuration.Nanoseconds())/1000000,
			float64(stat.MinDuration.Nanoseconds())/1000000,
			float64(stat.MaxDuration.Nanoseconds())/1000000)
	}

	saveCompleteResults(stats)
	generateFinalRecommendation(stats)
}

func saveCompleteResults(stats []ServerStats) {
	csvFile, err := os.Create("complete_dns_report.csv")
	if err != nil {
		log.Printf("创建CSV文件失败: %v", err)
		return
	}
	defer csvFile.Close()

	writer := bufio.NewWriter(csvFile)
	defer writer.Flush()

	writer.WriteString("服务器名称,地址,类型,分类,总测试数,成功数,失败数,成功率(%),平均延迟(ms),最小延迟(ms),最大延迟(ms),标准差(ms)\n")

	for _, stat := range stats {
		writer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%d,%d,%d,%.2f,%.2f,%.2f,%.2f,%.2f\n",
			stat.Server.Name,
			stat.Server.Address,
			stat.Server.Type,
			stat.Server.Category,
			stat.TotalTests,
			stat.Successful,
			stat.Failed,
			stat.SuccessRate,
			float64(stat.AvgDuration.Nanoseconds())/1000000,
			float64(stat.MinDuration.Nanoseconds())/1000000,
			float64(stat.MaxDuration.Nanoseconds())/1000000,
			float64(stat.StdDeviation.Nanoseconds())/1000000))
	}

	fmt.Println("\n📊 结果已保存到:")
	fmt.Println("  - complete_dns_report.csv (Excel可读)")
}

func generateFinalRecommendation(stats []ServerStats) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🎯 GUI.for.SingBox 最终推荐配置")
	fmt.Println(strings.Repeat("=", 80))

	var localServers, remoteServers []ServerStats
	var localUDP, remoteUDP []ServerStats

	for _, stat := range stats {
		if stat.SuccessRate < 85 {
			continue
		}

		if stat.Server.Category == "local" {
			localServers = append(localServers, stat)
			if stat.Server.Type == "udp" {
				localUDP = append(localUDP, stat)
			}
		} else {
			remoteServers = append(remoteServers, stat)
			if stat.Server.Type == "udp" {
				remoteUDP = append(remoteUDP, stat)
			}
		}
	}

	var localDNS, localResolver, remoteDNS, remoteResolver string

	if len(localServers) > 0 {
		localDNS = localServers[0].Server.Address
		if len(localUDP) > 0 {
			localResolver = localUDP[0].Server.Address
		} else {
			localResolver = "*********"
		}
	} else {
		localDNS = "*********"
		localResolver = "*********"
	}

	if len(remoteServers) > 0 {
		remoteDNS = remoteServers[0].Server.Address
		if len(remoteUDP) > 0 {
			remoteResolver = remoteUDP[0].Server.Address
		} else {
			remoteResolver = "*******"
		}
	} else {
		remoteDNS = "*******"
		remoteResolver = "*******"
	}

	fmt.Printf("📍 Local DNS:          %s\n", localDNS)
	fmt.Printf("📍 Local DNS Resolver: %s\n", localResolver)
	fmt.Printf("🌍 Remote DNS:         %s\n", remoteDNS)
	fmt.Printf("🌍 Remote DNS Resolver:%s\n", remoteResolver)

	// 性能分析
	typeStats := make(map[string][]ServerStats)
	for _, stat := range stats {
		if stat.SuccessRate >= 80 {
			typeStats[stat.Server.Type] = append(typeStats[stat.Server.Type], stat)
		}
	}

	fmt.Println("\n📈 性能分析:")
	for dnsType, servers := range typeStats {
		if len(servers) > 0 {
			avgLatency := float64(0)
			for _, s := range servers {
				avgLatency += float64(s.AvgDuration.Nanoseconds()) / 1000000
			}
			avgLatency /= float64(len(servers))

			displayType := strings.ToUpper(dnsType)
			if dnsType == "dot-innovative" {
				displayType = "DOT-创新"
			}

			fmt.Printf("  %s: %d个可用, 平均延迟 %.1fms\n",
				displayType, len(servers), avgLatency)
		}
	}
}
