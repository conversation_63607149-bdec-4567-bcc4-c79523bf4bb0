dns:
  enable: true
  listen: "0.0.0.0:53"
  enhanced-mode: "fake-ip"
  fake-ip-range: "**********/16"
  fake-ip-filter-mode: "blacklist"
  prefer-h3: true
  respect-rules: true
  use-hosts: false
  use-system-hosts: false
  ipv6: false
  fake-ip-filter:
    - "*.lan"
    - "*.local"
    - "*.arpa"
    - "dns.msftncsi.com"
    - "www.msftncsi.com"
    - "*.msftncsi.com"
    - "www.msftconnecttest.com"
    - "time.*.com"
    - "ntp.*.com"
    - "*.srv.nintendo.net"
    - "*.stun.playstation.net"
    - "xbox.*.microsoft.com"
    - "*.xboxlive.com"
    - "localhost.ptlogin2.qq.com"
    - "*.teafone.com"
    - "*.sktswe.net"
    - "rtc.goodfone.co.kr"
    - "*.chattti.com"
    - "*.market.xiaomi.com"
  
  # 🚀 基础DNS服务器（极速UDP，用于系统启动和基础解析）
  default-nameserver:
    - "*********"      # 阿里DNS (12ms) - 国内最优
    - "*******"        # Cloudflare DNS (10ms) - 国际最优  
    - "***************" # 114DNS (10ms) - 备用稳定
  
  # 🎯 主要DNS服务器（平衡性能与安全）
  nameserver:
    - "https://dns.alidns.com/dns-query"     # 阿里DoH (110ms) - 最快加密DNS
    - "tls://*********#853"                  # 阿里DoT (120ms) - 国内最快TLS
    - "tls://**********#853"                 # 114DoT (262ms) - 国内备用TLS
    - "tls://************#853"               # OpenDNS创新DoT (265ms) - 国际优秀TLS
    - "https://public.dns.iij.jp/dns-query"  # IIJ DoH (335ms) - 日本优质服务
    - "tls://*******#853"                    # Quad9 DoT (332ms) - 国际安全标准
  
  # 🔄 直连DNS策略
  direct-nameserver-follow-policy: true
  
  # 🛡️ 回退过滤器（优化地理位置判断）
  fallback-filter:
    geoip: true
    geoip-code: "CN"
    ipcidr:
      - "240.0.0.0/4"
      - "0.0.0.0/32"
    domain:
      - "+.google.com"
      - "+.facebook.com" 
      - "+.youtube.com"
      - "+.twitter.com"
      - "+.instagram.com"
      - "+.telegram.org"
      - "+.github.com"
      - "+.stackoverflow.com"
  
  # 🌍 国际回退DNS（全TLS加密，隐私优先）
  fallback:
    - "tls://*******#853"                    # Cloudflare DoT - 国际标准
    - "tls://*******#853"                    # Google创新DoT (366ms) - 可靠备用
    - "https://doh.opendns.com/dns-query"    # OpenDNS DoH (336ms) - 备用加密
    - "tls://************#853"               # AdGuard创新DoT (340ms) - 广告过滤
  
  # 🔧 代理服务器DNS（高性能TLS，用于代理节点解析）
  proxy-server-nameserver:
    - "https://dns.alidns.com/dns-query"     # 阿里DoH (110ms) - 最快加密
    - "tls://*********#853"                  # 阿里DoT (120ms) - 国内最快TLS
    - "tls://**********#853"                 # 114DoT (262ms) - 国内备用
    - "tls://************#853"               # OpenDNS创新DoT (265ms) - 国际备用
  
  # 📍 直连DNS（保持空，使用nameserver）
  direct-nameserver: []
  
  # 🎯 域名策略DNS（针对特定域名优化）
  nameserver-policy:
    # 国内域名使用最快UDP DNS
    "geosite:cn":
      - "*********"      # 阿里DNS (12ms)
      - "119.29.29.29"    # 腾讯DNS (17ms) 
      - "***************" # 114DNS (10ms)
    
    # 国际域名使用快速UDP + TLS备用
    "geosite:geolocation-!cn":
      - "*******"                           # Cloudflare UDP (10ms)
      - "8.8.8.8"                           # Google UDP (10ms)
      - "tls://************#853"            # OpenDNS创新DoT备用
    
    # 特殊域名使用最安全的加密DNS
    "+.google.com,+.youtube.com,+.github.com":
      - "https://dns.alidns.com/dns-query"   # 阿里DoH (110ms)
      - "tls://*********#853"                # 阿里DoT (120ms)
      - "tls://*******#853"                  # Quad9 DoT (332ms)
