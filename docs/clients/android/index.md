---
icon: material/android
---

# sing-box for Android

SFA allows users to manage and run local or remote sing-box configuration files, and provides
platform-specific function implementation, such as TUN transparent proxy implementation.

## :material-graph: Requirements

* Android 5.0+

## :material-download: Download

* [Play Store](https://play.google.com/store/apps/details?id=io.nekohasekai.sfa)
* [Play Store (Beta)](https://play.google.com/apps/testing/io.nekohasekai.sfa)
* [GitHub Releases](https://github.com/SagerNet/sing-box/releases)
* [F-Droid](https://f-droid.org/packages/io.nekohasekai.sfa/) (Unified signature via reproducible builds)

## :material-source-repository: Source code

* [GitHub](https://github.com/SagerNet/sing-box-for-android)
