---
icon: material/book-lock-open
---

# TunnelVision

TunnelVision is an attack that uses DHCP option 121 to set higher priority routes
so that traffic does not go through the VPN.

Reference: https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-3661

## Status

### Android

Android does not handle DHCP option 121 and is not affected.

### Apple platforms

Update [sing-box graphical client](/clients/apple/#download) to `1.9.0-rc.16` or newer,
then enable `includeAllNetworks` in `Settings` — `Packet Tunnel` and you will be unaffected.

Note: when `includeAllNetworks` is enabled, the default TUN stack is changed to `gvisor`,
and the `system` and `mixed` stacks are not available.

### Linux

Update sing-box to `1.9.0-rc.16` or newer, rules generated by `auto-route` are unaffected.

### Windows

No solution yet.

## Workarounds

* Don't connect to untrusted networks
* Relay untrusted network through another device
* Just ignore it
