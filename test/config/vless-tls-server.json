{"log": {"loglevel": "debug"}, "inbounds": [{"listen": "0.0.0.0", "port": 1234, "protocol": "vless", "settings": {"decryption": "none", "clients": [{"id": "b831381d-6324-4d53-ad4f-8cda48b30811", "flow": ""}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"serverName": "example.org", "certificates": [{"certificateFile": "/path/to/certificate.crt", "keyFile": "/path/to/private.key"}]}}}], "outbounds": [{"protocol": "freedom"}]}