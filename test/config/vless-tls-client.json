{"log": {"loglevel": "debug"}, "inbounds": [{"listen": "0.0.0.0", "port": "1080", "protocol": "socks", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "ip": "127.0.0.1"}}], "outbounds": [{"protocol": "vless", "settings": {"vnext": [{"address": "host.docker.internal", "port": 1234, "users": [{"id": "", "encryption": "none", "flow": ""}]}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"serverName": "example.org", "certificates": [{"certificateFile": "/path/to/certificate.crt", "keyFile": "/path/to/private.key"}], "fingerprint": "chrome"}}}]}