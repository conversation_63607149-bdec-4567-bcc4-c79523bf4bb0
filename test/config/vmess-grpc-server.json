{"log": {"loglevel": "debug"}, "inbounds": [{"listen": "0.0.0.0", "port": 1234, "protocol": "vmess", "settings": {"clients": [{"id": "b831381d-6324-4d53-ad4f-8cda48b30811"}]}, "streamSettings": {"network": "gun", "security": "tls", "tlsSettings": {"serverName": "example.org", "certificates": [{"certificateFile": "/path/to/certificate.crt", "keyFile": "/path/to/private.key"}]}, "grpcSettings": {"serviceName": "TunService"}}}], "outbounds": [{"protocol": "freedom"}]}